import mysql.connector

# Database configuration
db_config = {
    'host': '**************',
    'user': 'giggenius_user',
    'password': 'Happiness1524!',
    'database': 'giggenius',
    'connect_timeout': 10,
    'use_pure': True,
    'autocommit': True,
    'buffered': True,
    'consume_results': True
}

try:
    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor(dictionary=True)

    # First, check the portfolios table structure
    print("📋 Portfolio table structure:")
    cursor.execute("DESCRIBE portfolios")
    columns = cursor.fetchall()
    for col in columns:
        print(f"  - {col['Field']}: {col['Type']}")
    print()

    # Check what portfolio data exists in portfolios table
    cursor.execute('SELECT * FROM portfolios ORDER BY created_at DESC')
    results = cursor.fetchall()

    print(f'📊 Found {len(results)} portfolio entries:')
    if results:
        for row in results:
            print(f'  Portfolio ID {row["id"]}:')
            print(f'    - Genius ID: {row["genius_id"]}')
            print(f'    - Title: "{row["project_title"]}"')
            print(f'    - URL: {row["project_url"]}')
            print(f'    - Role: {row["your_role"]}')
            print(f'    - Skills: {row["skills"]}')
            print(f'    - Status: {row["status"]}')
            print(f'    - Created: {row["created_at"]}')
            print()
    else:
        print("  No portfolio entries found.")

    cursor.close()
    conn.close()

except Exception as e:
    print(f'Error: {e}')
